package router

import (
	"boce-service/controller"
	"github.com/gin-gonic/gin"
	"net/http"
)

func InitRouter() *gin.Engine {
	r := gin.New()

	// 使用Logger和Recovery中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	// 定义路由
	r.GET("/ping", func(c *gin.Context) {
		c.String(http.StatusOK, "pong")
	})

	// IP元数据查询接口
	r.GET("/api/ip/aw_meta", controller.GetIpMeta)
	r.GET("/api/dns/reload", controller.ReloadDnsList)
	r.POST("/api/ip/register", controller.IpRegister)
	r.GET("/api/ip", controller.GetClientIp)
	r.GET("/api/node", controller.GetNode)

	// Item管理接口
	r.POST("/api/items/batch", controller.BatchCreateOrUpdateItems)
	r.POST("/api/items", controller.CreateItem)
	r.GET("/api/items", controller.GetItems)
	r.PUT("/api/items", controller.UpdateItem)
	r.DELETE("/api/items/:name", controller.DeleteItem)

	// Node管理接口
	r.GET("/api/nodes", controller.GetAllNodes)
	r.PUT("/api/nodes/status", controller.UpdateNodeStatus)
	r.POST("/api/nodes/:ip/offline", controller.OfflineNode)

	// DNS管理接口
	r.GET("/api/dns", controller.GetAllDNS)
	r.POST("/api/dns", controller.CreateDNS)
	r.POST("/api/dns/batch", controller.BatchCreateDNS)

	// WebSocket文件推送接口
	r.GET("/ws", controller.WSHandler)
	r.POST("/ws/api/files/push", controller.PushFiles)
	r.GET("/ws/api/clients", controller.GetConnectedClients)
	r.POST("/ws/api/push/configs", controller.PushNodeAllConfigFiles)
	r.POST("/ws/api/push/node", controller.PushConfigToNode)
	r.POST("/ws/api/reload/blackbox", controller.ReloadBlackboxConfig)

	// Blackbox配置生成接口
	r.GET("/api/blackbox/generate/base64", controller.GenerateBlackboxConfig)

	// VMAgent配置生成接口
	r.GET("/api/vmagent/generate", controller.GenerateVMAgentConfig)

	// 配置生成接口
	//r.GET("/api/configs/generate/all", controller.GenerateAllConfigsDo)
	r.GET("/api/configs/generate/items", controller.GenerateItemsJson)
	r.GET("/api/configs/generate/nodeinfo", controller.GenerateNodeInfoJson)

	// 静态文件服务
	r.Static("/web", "./web")
	r.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/web/")
	})

	return r
}
