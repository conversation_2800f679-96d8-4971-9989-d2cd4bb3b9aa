package controller

import (
	"boce-service/service"
	"boce-service/utils/log"
	"github.com/gin-gonic/gin"
	"net/http"
	"time"
)

// VMAlertWebhook 处理 vmalert 发送的告警
func VMAlertWebhook(c *gin.Context) {
	var alerts service.VMAlertPayload
	if err := c.ShouldBindJSON(&alerts); err != nil {
		log.Error("Failed to parse vmalert webhook payload: %v", err)
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid JSON payload"})
		return
	}

	log.Info("Received vmalert webhook with %d alerts", len(alerts.Alerts))

	// 处理每个告警
	for _, alert := range alerts.Alerts {
		if err := service.ProcessVMAlert(alert); err != nil {
			log.Error("Failed to process vmalert alert %s: %v", alert.Labels["alertname"], err)
		}
	}

	c.<PERSON>(http.StatusOK, gin.H{"status": "success", "processed": len(alerts.Alerts)})
}

// AlertmanagerWebhook 处理 alertmanager 发送的告警
func AlertmanagerWebhook(c *gin.Context) {
	var payload service.AlertmanagerPayload
	if err := c.ShouldBindJSON(&payload); err != nil {
		log.Error("Failed to parse alertmanager webhook payload: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid JSON payload"})
		return
	}

	log.Info("Received alertmanager webhook with %d alerts, status: %s", len(payload.Alerts), payload.Status)

	// 处理每个告警
	for _, alert := range payload.Alerts {
		if err := service.ProcessAlertmanagerAlert(alert, payload.Status); err != nil {
			log.Error("Failed to process alertmanager alert %s: %v", alert.Labels["alertname"], err)
		}
	}

	c.JSON(http.StatusOK, gin.H{"status": "success", "processed": len(payload.Alerts)})
}

// UnifiedWebhook 统一处理 vmalert 和 alertmanager 的告警
func UnifiedWebhook(c *gin.Context) {
	var rawPayload map[string]interface{}
	if err := c.ShouldBindJSON(&rawPayload); err != nil {
		log.Error("Failed to parse webhook payload: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid JSON payload"})
		return
	}

	// 判断是来自 vmalert 还是 alertmanager
	if _, hasStatus := rawPayload["status"]; hasStatus {
		// 这是 alertmanager 的格式
		var payload service.AlertmanagerPayload
		if err := c.ShouldBindJSON(&payload); err != nil {
			log.Error("Failed to parse alertmanager payload: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid alertmanager payload"})
			return
		}

		log.Info("Detected alertmanager webhook with %d alerts, status: %s", len(payload.Alerts), payload.Status)
		
		for _, alert := range payload.Alerts {
			if err := service.ProcessAlertmanagerAlert(alert, payload.Status); err != nil {
				log.Error("Failed to process alertmanager alert %s: %v", alert.Labels["alertname"], err)
			}
		}
		
		c.JSON(http.StatusOK, gin.H{"status": "success", "source": "alertmanager", "processed": len(payload.Alerts)})
	} else {
		// 这是 vmalert 的格式
		var alerts service.VMAlertPayload
		if err := c.ShouldBindJSON(&alerts); err != nil {
			log.Error("Failed to parse vmalert payload: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid vmalert payload"})
			return
		}

		log.Info("Detected vmalert webhook with %d alerts", len(alerts.Alerts))
		
		for _, alert := range alerts.Alerts {
			if err := service.ProcessVMAlert(alert); err != nil {
				log.Error("Failed to process vmalert alert %s: %v", alert.Labels["alertname"], err)
			}
		}
		
		c.JSON(http.StatusOK, gin.H{"status": "success", "source": "vmalert", "processed": len(alerts.Alerts)})
	}
}

// WebhookHealth 健康检查接口
func WebhookHealth(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": time.Now().Unix(),
		"service":   "webhook-alert-service",
	})
}
