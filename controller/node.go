package controller

import (
	"boce-service/model"
	"boce-service/utils/log"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"net"
	"net/http"
)

// ItemsResponse Items响应结构

// RegisterResponse 注册响应结构
type RegisterResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Items    Items          `json:"items"`
		Node     model.NodeData `json:"node"`
		Blackbox Blackbox       `json:"blackbox"`
		Vmagent  struct {
			ConfigContent string `json:"config_content"`
			ConfigBase64  string `json:"config_base64"`
		} `json:"vmagent"`
	} `json:"data"`
}

func NodeMatchDNS(node model.Node) (int, string, error) {
	var cityDnsList, provinceDnsList []model.DNS
	var dnsAddr string
	var dnsId int
	if node.Continent == "保留IP" {
		log.Infof("保留IP: %s，跳过匹配DNS", node.IpAddr)
		return 0, "", nil
	}
	cityDnsList = model.GetDNSByCityAndISP(node.City, node.Province, node.Isp)
	if len(cityDnsList) > 0 {
		dnsId = cityDnsList[0].Id
		dnsAddr = cityDnsList[0].IpAddr
	}

	if len(cityDnsList) == 0 {
		log.Infof("未找到ISP %s 在 %s 的DNS记录，开始查找ISP %s 在 %s 的DNS记录", node.Isp, node.City, node.Isp, node.Province)
		provinceDnsList = model.GetDNSByProvinceAndISP(node.Province, node.Isp)
		if len(provinceDnsList) > 0 {
			dnsId = provinceDnsList[0].Id
			dnsAddr = provinceDnsList[0].IpAddr
		} else {
			log.Infof("未找到ISP %s 在 %s 的DNS记录，开始查找ISP %s 的DNS记录", node.Isp, node.Province, node.Isp)
		}
	}

	if dnsId == 0 {
		log.Infof("节点未能找到任何匹配省份和城市的DNS记录", node.Isp)
	}

	return dnsId, dnsAddr, nil
}

func IpRegister(c *gin.Context) {
	if c.Request.Method != "POST" {
		c.JSON(http.StatusMethodNotAllowed, gin.H{
			"code":    405,
			"message": "Method Not Allowed",
		})
		return
	}
	var clientIP string
	if c.Query("ip") != "" {
		clientIP = c.Query("ip")
		log.Info("clientIP:", c.Query("ip"))

	} else {
		clientIP = c.ClientIP()
		log.Info("clientIP:", clientIP)
	}

	// 判断IP协议版本
	ipProtocol := getIPProtocol(clientIP)
	log.Infof("检测到IP %s 协议版本: %s", clientIP, ipProtocol)

	err, res := GetAwMeta(clientIP)
	if err != nil {
		log.Info("查询IP信息失败:", err)
	}
	log.Info("查询IP信息成功:", res)

	node := model.Node{
		Id:         uuid.New(),
		IpAddr:     clientIP,
		Accuracy:   getStringValue(res, "accuracy"),
		Continent:  getStringValue(res, "continent"),
		Country:    getStringValue(res, "country"),
		Province:   getStringValue(res, "province"),
		City:       getStringValue(res, "city"),
		District:   getStringValue(res, "district"),
		AreaCode:   getStringValue(res, "areacode"),
		Isp:        getStringValue(res, "isp"),
		ZipCode:    getStringValue(res, "zipcode"),
		AsNumber:   getStringValue(res, "asnumber"),
		Lngwgs:     getStringValue(res, "lngwgs"),
		Latwgs:     getStringValue(res, "latwgs"),
		Status:     model.NODE_ON_STATUS,
		Owner:      getStringValue(res, "owner"),
		AdCode:     getStringValue(res, "adcode"),
		IpProtocol: ipProtocol, // 设置IP协议版本
	}
	node.IspCode = model.ISP_MAP[node.Isp]
	if node.IspCode == "" {
		log.Errorf("查询ISP %s 代码失败: %v", node.Isp, err)
	}
	node.DnsId, _, err = NodeMatchDNS(node)
	err = model.CreateOrUpdateNode(node)
	if err != nil {
		log.Error("写入数据库失败:%s", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "写入数据库失败",
		})
		return
	}

	response, err := GetGenerateAllConfigs(clientIP)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "生成配置失败",
		})
		return
	}

	// 构建符合RegisterResponse结构的响应
	registerResponse := RegisterResponse{
		Code:    200,
		Message: "节点注册成功",
	}

	registerResponse.Data.Items = response.ItemsJson
	registerResponse.Data.Node = response.NodeInfoJson
	registerResponse.Data.Vmagent = response.VmagentYaml
	registerResponse.Data.Blackbox = response.BlackboxYaml

	c.JSON(http.StatusOK, registerResponse)
}

func GetClientIp(c *gin.Context) {
	clientIP := c.ClientIP()
	c.JSON(http.StatusOK, gin.H{
		"client_ip": clientIP,
	})
}

func GetNode(c *gin.Context) {
	var node model.Node
	if c.Request.Method != "GET" {
		c.JSON(http.StatusMethodNotAllowed, gin.H{
			"code":    405,
			"message": "Method Not Allowed",
		})
		return
	}
	if c.Query("ip") != "" {
		node.IpAddr = c.Query("ip")
		log.Info("clientIP:", c.Query("ip"))

	} else {
		node.IpAddr = c.ClientIP()
		log.Info("clientIP:", node.IpAddr)
	}
	if node.IpAddr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "IP地址不能为空",
		})
		return
	}
	data, err := model.GetNodeData(node.IpAddr)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "查询成功",
		"data":    data,
	})
}

// getIPProtocol 判断IP地址是IPv4还是IPv6
func getIPProtocol(ipAddr string) string {
	ip := net.ParseIP(ipAddr)
	if ip == nil {
		log.Warnf("无效的IP地址: %s", ipAddr)
		return "unknown"
	}

	// 检查是否为IPv4
	if ip.To4() != nil {
		return "ip4"
	}

	// 检查是否为IPv6
	if ip.To16() != nil {
		return "ip6"
	}

	return "unknown"
}

// GetAllNodes 获取所有节点列表
func GetAllNodes(c *gin.Context) {
	if c.Request.Method != "GET" {
		c.JSON(http.StatusMethodNotAllowed, gin.H{
			"code":    405,
			"message": "Method Not Allowed",
		})
		return
	}

	nodes := model.GetAllNodes()
	var nodeDataList []model.NodeData

	for _, node := range nodes {
		dns := model.GetDNSById(node.DnsId)
		nodeData := model.NodeData{
			Id:         node.Id,
			IpAddr:     node.IpAddr,
			Continent:  node.Continent,
			City:       node.City,
			Province:   node.Province,
			Isp:        node.Isp,
			IspCode:    node.IspCode,
			DnsAddr:    dns.IpAddr,
			ZipCode:    node.ZipCode,
			Latwgs:     node.Latwgs,
			Lngwgs:     node.Lngwgs,
			IpProtocol: node.IpProtocol,
			Status:     node.Status,
		}
		nodeDataList = append(nodeDataList, nodeData)
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "查询成功",
		"data":    nodeDataList,
		"count":   len(nodeDataList),
	})
}

// UpdateNodeStatus 更新节点状态
func UpdateNodeStatus(c *gin.Context) {
	var req struct {
		IpAddr string `json:"ip_addr" binding:"required"`
		Status int    `json:"status" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数格式错误",
		})
		return
	}

	node, err := model.GetNodeByIp(req.IpAddr)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "节点不存在",
		})
		return
	}

	node.Status = req.Status
	if err := model.CreateOrUpdateNode(node); err != nil {
		log.Error("更新节点状态失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "节点状态更新成功",
	})
}

// OfflineNode 下线指定节点
func OfflineNode(c *gin.Context) {
	nodeIP := c.Param("ip")
	if nodeIP == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "节点IP不能为空",
		})
		return
	}

	// 更新节点状态为禁用
	err := model.UpdateNodeStatus(nodeIP, model.NODE_OFF_STATUS)
	if err != nil {
		log.Error("更新节点状态失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "下线节点失败",
		})
		return
	}

	// 发送下线信号到WebSocket客户端
	// 这里需要调用WebSocket服务来关闭连接和发送下线信号
	// 可以通过消息队列或直接调用WebSocket管理器

	log.Infof("节点 %s 已下线", nodeIP)
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "节点下线成功",
	})
}
