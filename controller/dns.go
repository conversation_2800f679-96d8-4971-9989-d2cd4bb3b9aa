package controller

import (
	"boce-service/config"
	"boce-service/model"
	"boce-service/utils/awdb"
	"boce-service/utils/log"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"os"
	"strings"
	"sync"
)

// GetIpMeta 获取IP元数据
func GetIpMeta(c *gin.Context) {
	// 从请求参数中获取IP地址
	ipaddr := c.Query("ip")
	if ipaddr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "IP地址不能为空",
		})
		return
	}

	err, res := GetAwMeta(ipaddr)
	if err != nil {
		log.Error("查询IP信息失败:", err)
		c.J<PERSON>(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询IP信息失败",
		})
		return
	}

	// 返回查询结果
	c.<PERSON>(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data":    res,
	})
}

func GetAwMeta(ipaddr string) (error, map[string]interface{}) {
	filesConfig := config.GetFilesConfig("")
	awdbfile, err := awdb.Openfile(filesConfig.AWDBPath)
	defer awdbfile.Closefile()

	// 查询IP信息
	err, res := awdbfile.SearchIP(ipaddr)
	if err != nil {
		log.Error(err)
	}
	return err, res
}

func ReloadDnsList(c *gin.Context) {
	log.Info("开始重载DNS数据...")

	filesConfig := config.GetFilesConfig("")
	content, err := os.ReadFile(filesConfig.DNSListPath)
	if err != nil {
		log.Error("读取dns_list文件失败:", err)
		return
	}
	// 按行分割文件内容
	lines := strings.Split(string(content), "\n")

	var wg sync.WaitGroup
	results := make(chan model.DNS, len(lines))

	// 并发查询每个IP地址的元数据
	for _, line := range lines {
		ipaddr := strings.TrimSpace(line)
		if ipaddr == "" {
			continue
		}
		wg.Add(1)
		go func(ip string) {
			defer wg.Done()

			// 使用GetAwMeta查询IP元数据
			err, meta := GetAwMeta(ip)
			if err != nil {
				log.Errorf("查询IP %s 元数据失败: %v", ip, err)
				return
			}
			// 创建DNS记录
			dns := model.DNS{
				IpAddr:    ip,
				Accuracy:  getStringValue(meta, "accuracy"),
				Continent: getStringValue(meta, "continent"),
				Country:   getStringValue(meta, "country"),
				Province:  getStringValue(meta, "province"),
				City:      getStringValue(meta, "city"),
				District:  getStringValue(meta, "district"),
				AreaCode:  getStringValue(meta, "areacode"),
				Isp:       getStringValue(meta, "isp"),
				ZipCode:   getStringValue(meta, "zipcode"),
				AsNumber:  getStringValue(meta, "asnumber"),
				Lngwgs:    getStringValue(meta, "lngwgs"),
				Latwgs:    getStringValue(meta, "latwgs"),
				Status:    model.DNS_ON_STATUS,
				Owner:     getStringValue(meta, "owner"),
				AdCode:    getStringValue(meta, "adcode"),
			}
			dns.IspCode = model.ISP_MAP[dns.Isp]
			if dns.IspCode == "" {
				log.Warnf("%s 查询ISP %s 代码失败: %v", dns.IpAddr, dns.Isp, err)
			}
			results <- dns
		}(ipaddr)
	}

	// 等待所有查询完成
	go func() {
		wg.Wait()
		close(results)
	}()

	// 收集结果
	var dnsRecords []model.DNS
	for dns := range results {
		dnsRecords = append(dnsRecords, dns)
	}

	log.Infof("成功查询到 %d 条DNS记录，开始写入数据库...", len(dnsRecords))

	// 这里可以添加数据库插入逻辑
	err = model.BatchCreateOrUpdateDNS(dnsRecords)
	if err != nil {
		log.Error("写入数据库失败:%s", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "dns列表重载失败",
		})
		return
	}
	log.Info("DNS数据重载完成")
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success"})
}

// 从元数据中获取字符串值
func getStringValue(meta map[string]interface{}, key string) string {
	if value, ok := meta[key]; ok {
		if strValue, ok := value.(string); ok {
			return strValue
		}
	}
	return ""
}

// GetAllDNS 获取所有DNS列表
func GetAllDNS(c *gin.Context) {
	if c.Request.Method != "GET" {
		c.JSON(http.StatusMethodNotAllowed, gin.H{
			"code":    405,
			"message": "Method Not Allowed",
		})
		return
	}

	dnsList := model.GetDNSbyFilter("status = 1")

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "查询成功",
		"data":    dnsList,
		"count":   len(dnsList),
	})
}

// CreateDNS 创建DNS记录
func CreateDNS(c *gin.Context) {
	var req struct {
		IpAddr string `json:"ip_addr" binding:"required"`
		Owner  string `json:"owner"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数格式错误",
		})
		return
	}

	// 查询IP元数据
	err, meta := GetAwMeta(req.IpAddr)
	if err != nil {
		log.Error("查询IP信息失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询IP信息失败",
		})
		return
	}

	// 创建DNS记录
	dns := model.DNS{
		IpAddr:    req.IpAddr,
		Accuracy:  getStringValue(meta, "accuracy"),
		Continent: getStringValue(meta, "continent"),
		Country:   getStringValue(meta, "country"),
		Province:  getStringValue(meta, "province"),
		City:      getStringValue(meta, "city"),
		District:  getStringValue(meta, "district"),
		AreaCode:  getStringValue(meta, "areacode"),
		Isp:       getStringValue(meta, "isp"),
		ZipCode:   getStringValue(meta, "zipcode"),
		AsNumber:  getStringValue(meta, "asnumber"),
		Lngwgs:    getStringValue(meta, "lngwgs"),
		Latwgs:    getStringValue(meta, "latwgs"),
		Status:    model.DNS_ON_STATUS,
		Owner:     req.Owner,
		AdCode:    getStringValue(meta, "adcode"),
	}
	dns.IspCode = model.ISP_MAP[dns.Isp]

	// 保存到数据库
	if err := model.BatchCreateOrUpdateDNS([]model.DNS{dns}); err != nil {
		log.Error("创建DNS记录失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "DNS记录创建成功",
		"data":    dns,
	})
}

// BatchCreateDNS 批量创建DNS记录
func BatchCreateDNS(c *gin.Context) {
	var req struct {
		IpList []string `json:"ip_list" binding:"required"`
		Owner  string   `json:"owner"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数格式错误",
		})
		return
	}

	if len(req.IpList) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "IP列表不能为空",
		})
		return
	}

	var dnsRecords []model.DNS
	var wg sync.WaitGroup
	results := make(chan model.DNS, len(req.IpList))
	errors := make(chan error, len(req.IpList))

	// 并发查询每个IP的元数据
	for _, ipAddr := range req.IpList {
		wg.Add(1)
		go func(ip string) {
			defer wg.Done()

			// 查询IP元数据
			err, meta := GetAwMeta(ip)
			if err != nil {
				log.Errorf("查询IP %s 信息失败: %v", ip, err)
				errors <- err
				return
			}

			// 创建DNS记录
			dns := model.DNS{
				IpAddr:    ip,
				Accuracy:  getStringValue(meta, "accuracy"),
				Continent: getStringValue(meta, "continent"),
				Country:   getStringValue(meta, "country"),
				Province:  getStringValue(meta, "province"),
				City:      getStringValue(meta, "city"),
				District:  getStringValue(meta, "district"),
				AreaCode:  getStringValue(meta, "areacode"),
				Isp:       getStringValue(meta, "isp"),
				ZipCode:   getStringValue(meta, "zipcode"),
				AsNumber:  getStringValue(meta, "asnumber"),
				Lngwgs:    getStringValue(meta, "lngwgs"),
				Latwgs:    getStringValue(meta, "latwgs"),
				Status:    model.DNS_ON_STATUS,
				Owner:     req.Owner,
				AdCode:    getStringValue(meta, "adcode"),
			}
			dns.IspCode = model.ISP_MAP[dns.Isp]

			results <- dns
		}(ipAddr)
	}

	// 等待所有查询完成
	go func() {
		wg.Wait()
		close(results)
		close(errors)
	}()

	// 收集结果
	for dns := range results {
		dnsRecords = append(dnsRecords, dns)
	}

	// 检查是否有错误
	var errorCount int
	for range errors {
		errorCount++
	}

	if len(dnsRecords) == 0 {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "所有IP查询失败",
		})
		return
	}

	// 保存到数据库
	if err := model.BatchCreateOrUpdateDNS(dnsRecords); err != nil {
		log.Error("批量创建DNS记录失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "批量创建失败",
		})
		return
	}

	message := fmt.Sprintf("成功创建 %d 条DNS记录", len(dnsRecords))
	if errorCount > 0 {
		message += fmt.Sprintf("，%d 条记录查询失败", errorCount)
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": message,
		"data": gin.H{
			"success_count": len(dnsRecords),
			"error_count":   errorCount,
		},
	})
}
