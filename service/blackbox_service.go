package service

import (
	"boce-service/model"
	"boce-service/utils/log"
	"bytes"
	"embed"
	"encoding/base64"
	"fmt"
	"strings"
	"text/template"
	"time"
)

//go:embed template/blackbox_exporter.tmpl
var templateFS embed.FS

// BlackboxService blackbox配置生成服务
type BlackboxService struct {
	templatePath string
	outputPath   string
}

// NewBlackboxService 创建blackbox服务实例
func NewBlackboxService(outputPath string) *BlackboxService {
	return &BlackboxService{
		templatePath: "template/blackbox_exporter.tmpl",
		outputPath:   outputPath,
	}
}

// GenerateFromItemsContent 从Item记录生成blackbox配置并返回原始内容
func (bs *BlackboxService) GenerateFromItemsContent(items []model.Item, nodeData model.NodeData) (string, error) {
	var modules []model.BlackboxModule

	for _, item := range items {
		if item.Status != model.ITEM_ON_STATUS {
			continue
		}

		module := bs.ConvertItemToModule(item, nodeData)
		if module != nil {
			modules = append(modules, *module)
		}
	}

	if len(modules) == 0 {
		log.Warn("没有找到有效的Item记录，使用默认配置")
		modules = []model.BlackboxModule{}
		// 为默认模块也设置DNS
		for i := range modules {
			if modules[i].HTTP != nil && nodeData.DnsAddr != "" {
				modules[i].HTTP.DnsServer = nodeData.DnsAddr
			}
		}
	}

	return bs.GenerateConfigContent(modules, nodeData.DnsAddr)
}

// GenerateConfigContent 生成blackbox_exporter.yaml配置并返回原始内容
func (bs *BlackboxService) GenerateConfigContent(modules []model.BlackboxModule, dnsAddr string) (string, error) {
	// 创建模板函数映射
	funcMap := template.FuncMap{
		"join": func(slice []string, sep string) string {
			return strings.Join(slice, sep)
		},
		"joinInt": func(slice []int, sep string) string {
			strSlice := make([]string, len(slice))
			for i, v := range slice {
				strSlice[i] = fmt.Sprintf("%d", v)
			}
			return strings.Join(strSlice, sep)
		},
	}

	// 从嵌入的文件系统读取模板
	tmplContent, err := templateFS.ReadFile("template/blackbox_exporter.tmpl")
	if err != nil {
		return "", fmt.Errorf("读取模板文件失败: %v", err)
	}

	// 解析模板
	tmpl, err := template.New("blackbox_exporter").Funcs(funcMap).Parse(string(tmplContent))
	if err != nil {
		return "", fmt.Errorf("解析模板失败: %v", err)
	}

	// 准备模板数据
	data := struct {
		Modules    []model.BlackboxModule
		DefaultDNS string
	}{
		Modules:    modules,
		DefaultDNS: dnsAddr,
	}

	// 执行模板到内存缓冲区
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", fmt.Errorf("执行模板失败: %v", err)
	}

	return buf.String(), nil
}

// convertItemToModule 将Item记录转换为BlackboxModule，支持默认配置和自定义配置
func (bs *BlackboxService) ConvertItemToModule(item model.Item, nodeData model.NodeData) *model.BlackboxModule {
	// 判断是否使用自定义配置
	isCustom := bs.IsCustomConfig(item)

	var moduleName string
	if !isCustom {
		log.Debugf("Item %s 使用默认配置", item.Name)
		return nil
	}
	// 自定义配置：使用 target_method_custom 格式
	moduleName = bs.generateCustomModuleName(item.Target, item.Method)

	module := &model.BlackboxModule{
		Name:    moduleName,
		Timeout: time.Duration(item.Interval) * time.Second,
	}

	switch item.Method {
	case model.ITEM_HTTP_GET_Method: // HTTP GET
		module.Prober = "http"
		module.HTTP = bs.buildCustomHTTPProbe(item, nodeData, "GET")
	case model.ITEM_HTTP_POST_Method: // HTTP POST
		module.Prober = "http"
		module.HTTP = bs.buildCustomHTTPProbe(item, nodeData, "POST")
	case model.ITEM_TCP_Method: // TCP
		module.Prober = "tcp"
		module.TCP = bs.buildCustomTCPProbe(item)
	case model.ITEM_ICMP_Method: // ICMP
		module.Prober = "icmp"
		module.ICMP = bs.buildCustomICMPProbe(item)
	case model.ITEM_DNS_Method: // DNS
		module.Prober = "dns"
		module.DNS = bs.buildCustomDNSProbe(item)

	default:
		log.Warnf("未知的方法类型: %s, 跳过Item: %s", item.Method, item.Name)
		return nil
	}

	return module
}

// IsCustomConfig 判断Item是否需要自定义配置
func (bs *BlackboxService) IsCustomConfig(item model.Item) bool {
	// 基于IsAdvanced字段判断是否使用自定义配置
	return item.IsAdvanced == model.ITEM_ADVANCED_CONFIG
}

// getDNSQueryTypeFromItem 从Item中获取DNS查询类型
func (bs *BlackboxService) getDNSQueryTypeFromItem(item model.Item) string {
	if item.ItemData == "" {
		return "A" // 默认返回A记录
	}

	probeData, _, err := item.GetDataStruct()
	if err != nil {
		return "A" // 解析失败时默认返回A记录
	}

	if dnsData, ok := probeData.(model.DNSProbe); ok {
		if dnsData.QueryType != "" {
			return dnsData.QueryType
		}
	}

	return "A" // 默认返回A记录
}

// generateCustomModuleName 生成自定义模块名称
func (bs *BlackboxService) generateCustomModuleName(target, method string) string {
	// 清理target中的特殊字符，只保留字母数字和点
	cleanTarget := strings.ReplaceAll(target, "://", "_")
	cleanTarget = strings.ReplaceAll(cleanTarget, "/", "_")
	cleanTarget = strings.ReplaceAll(cleanTarget, ":", "_")
	cleanTarget = strings.ReplaceAll(cleanTarget, "?", "_")
	cleanTarget = strings.ReplaceAll(cleanTarget, "&", "_")
	cleanTarget = strings.ReplaceAll(cleanTarget, "=", "_")
	cleanTarget = strings.ReplaceAll(cleanTarget, ".", "_")

	// 限制长度
	if len(cleanTarget) > 50 {
		cleanTarget = cleanTarget[:50]
	}

	switch method {
	case model.ITEM_HTTP_GET_Method:
		return fmt.Sprintf("%s_http_custom", cleanTarget)
	case model.ITEM_HTTP_POST_Method:
		return fmt.Sprintf("%s_http_post_custom", cleanTarget)
	case model.ITEM_TCP_Method:
		return fmt.Sprintf("%s_tcp_custom", cleanTarget)
	case model.ITEM_ICMP_Method:
		return fmt.Sprintf("%s_icmp_custom", cleanTarget)
	case model.ITEM_DNS_Method:
		return fmt.Sprintf("%s_dns_custom", cleanTarget)
	default:
		return fmt.Sprintf("%s_unknown_custom", cleanTarget)
	}
}

// GenerateConfigBase64 生成blackbox_exporter.yaml配置并返回base64字符串
func (bs *BlackboxService) GenerateConfigBase64(modules []model.BlackboxModule, dnsAddr string) (string, error) {

	// 返回base64编码的配置内容
	content, err := bs.GenerateConfigContent(modules, dnsAddr)
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString([]byte(content)), nil
}

// GenerateFromItemsBase64 从Item记录生成blackbox配置并返回base64字符串
// Item记录 -> BlackboxModule -> 生成blackbox_exporter.yaml -> base64
func (bs *BlackboxService) GenerateFromItemsBase64(items []model.Item, nodeData model.NodeData) (string, error) {
	var modules []model.BlackboxModule

	for _, item := range items {
		if item.Status != model.ITEM_ON_STATUS {
			continue
		}

		module := bs.ConvertItemToModule(item, nodeData)
		if module != nil {
			modules = append(modules, *module)
		}
	}

	if len(modules) == 0 {
		log.Warn("没有找到有效的Item记录，使用默认配置")
		modules = []model.BlackboxModule{}
	}

	return bs.GenerateConfigBase64(modules, nodeData.DnsAddr)
}

// buildDefaultHTTPProbe 构建默认HTTP探测配置
func (bs *BlackboxService) buildDefaultHTTPProbe(nodeData model.NodeData, method string) *model.HTTPProbe {
	return &model.HTTPProbe{
		ValidHTTPVersions: []string{"HTTP/1.1", "HTTP/2.0"},
		ValidStatusCodes:  []int{200},
		Method:            method,
		DnsServer:         nodeData.DnsAddr,
		NoFollowRedirects: false,
		FailIfSSL:         false,
		FailIfNotSSL:      false,
	}
}

// buildCustomHTTPProbe 构建自定义HTTP探测配置
func (bs *BlackboxService) buildCustomHTTPProbe(item model.Item, nodeData model.NodeData, method string) *model.HTTPProbe {
	probe := &model.HTTPProbe{
		Method:    method,
		DnsServer: nodeData.DnsAddr,
	}

	// 解析自定义配置
	if probeData, _, err := item.GetDataStruct(); err == nil {
		httpData := probeData.(model.HTTPProbe)

		// 使用自定义配置，如果没有则使用默认值
		if len(httpData.ValidHTTPVersions) > 0 {
			probe.ValidHTTPVersions = httpData.ValidHTTPVersions
		} else {
			probe.ValidHTTPVersions = []string{"HTTP/1.1", "HTTP/2.0"}
		}

		if len(httpData.ValidStatusCodes) > 0 {
			probe.ValidStatusCodes = httpData.ValidStatusCodes
		} else {
			probe.ValidStatusCodes = []int{200}
		}

		probe.Headers = httpData.Headers
		probe.Body = httpData.Body
		probe.NoFollowRedirects = httpData.NoFollowRedirects
		probe.FailIfSSL = httpData.FailIfSSL
		probe.FailIfNotSSL = httpData.FailIfNotSSL
	} else {
		// 解析失败，使用默认配置
		probe.ValidHTTPVersions = []string{"HTTP/1.1", "HTTP/2.0"}
		probe.ValidStatusCodes = []int{200}
	}

	return probe
}

// buildDefaultTCPProbe 构建默认TCP探测配置
func (bs *BlackboxService) buildDefaultTCPProbe() *model.TCPProbe {
	return &model.TCPProbe{
		PreferredIPProtocol: "ip4",
	}
}

// buildCustomTCPProbe 构建自定义TCP探测配置
func (bs *BlackboxService) buildCustomTCPProbe(item model.Item) *model.TCPProbe {
	probe := &model.TCPProbe{}

	if probeData, _, err := item.GetDataStruct(); err == nil {
		tcpData := probeData.(model.TCPProbe)
		probe.PreferredIPProtocol = tcpData.PreferredIPProtocol
		probe.QueryResponse = tcpData.QueryResponse
		probe.TLSConfig = tcpData.TLSConfig
	}

	// 如果没有设置协议，使用默认值
	if probe.PreferredIPProtocol == "" {
		probe.PreferredIPProtocol = "ip4"
	}

	return probe
}

// buildDefaultICMPProbe 构建默认ICMP探测配置
func (bs *BlackboxService) buildDefaultICMPProbe() *model.ICMPProbe {
	return &model.ICMPProbe{
		PreferredIPProtocol: "ip4",
	}
}

// buildCustomICMPProbe 构建自定义ICMP探测配置
func (bs *BlackboxService) buildCustomICMPProbe(item model.Item) *model.ICMPProbe {
	probe := &model.ICMPProbe{}

	if probeData, _, err := item.GetDataStruct(); err == nil {
		icmpData := probeData.(model.ICMPProbe)
		probe.PreferredIPProtocol = icmpData.PreferredIPProtocol
	}

	// 如果没有设置协议，使用默认值
	if probe.PreferredIPProtocol == "" {
		probe.PreferredIPProtocol = "ip4"
	}

	return probe
}

// buildDefaultDNSProbe 构建默认DNS探测配置
func (bs *BlackboxService) buildDefaultDNSProbe() *model.DNSProbe {
	return &model.DNSProbe{
		QueryType:   "A",
		ValidRcodes: []string{"NOERROR"},
	}
}

// buildCustomDNSProbe 构建自定义DNS探测配置
func (bs *BlackboxService) buildCustomDNSProbe(item model.Item) *model.DNSProbe {
	probe := &model.DNSProbe{
		QueryName: item.Target,
	}

	if probeData, _, err := item.GetDataStruct(); err == nil {
		dnsData := probeData.(model.DNSProbe)
		probe.QueryType = dnsData.QueryType
		probe.ValidRcodes = dnsData.ValidRcodes
		probe.ValidateAnswer = dnsData.ValidateAnswer
	}

	// 设置默认值
	if probe.QueryType == "" {
		probe.QueryType = "A"
	}
	if len(probe.ValidRcodes) == 0 {
		probe.ValidRcodes = []string{"NOERROR"}
	}

	return probe
}
