# VMAgent Configuration
# Generated automatically

global:
  scrape_interval: {{ .GlobalConfig.ScrapeInterval }}

scrape_configs:
{{- if .DefaultScrapeConfigs.HttpGet }}
  - job_name: blackbox_http_get_default
    scrape_interval: 30s
    metrics_path: /probe
    params:
      module: [ "http_get_default" ]
    static_configs:
      {{- if .DefaultScrapeConfigs.HttpGet.Targets }}
      - targets:
        {{- range .DefaultScrapeConfigs.HttpGet.Targets }}
        - "{{ . }}"
        {{- end }}
      {{- end }}
      {{- if .DefaultScrapeConfigs.HttpGet.Labels }}
        labels:
        {{- range $key, $value := .DefaultScrapeConfigs.HttpGet.Labels }}
           {{ $key }}: "{{ $value }}"
        {{- end }}
      {{- end }}
    relabel_configs:
      - source_labels: ["__address__"]
        target_label: "__param_target"
      - source_labels: ["__address__"]
        regex: "(https?)://(.+?)/(.*)"
        replacement: "$1"
        target_label: "http_protocol"
      - source_labels: ["__address__"]
        regex: "(https?)://(.+?)/(.*)"
        replacement: "$2"
        target_label: "target"
      - source_labels: ["__address__"]
        regex: "(https?)://(.+?)/(.*)"
        replacement: "$3"
        target_label: "uri"
      - source_labels: ["node_ip"]
        target_label: "instance"
      - target_label: "__address__"
        replacement: "127.0.0.1:9115"
{{- end }}

{{- if .DefaultScrapeConfigs.HttpPost }}
  - job_name: blackbox_http_post_default
    scrape_interval: 30s
    metrics_path: /probe
    params:
      module: [ "http_post_default" ]
    static_configs:
      {{- if .DefaultScrapeConfigs.HttpPost.Targets }}
      - targets:
        {{- range .DefaultScrapeConfigs.HttpPost.Targets }}
        - "{{ . }}"
        {{- end }}
      {{- end }}
      {{- if .DefaultScrapeConfigs.HttpPost.Labels }}
        labels:
        {{- range $key, $value := .DefaultScrapeConfigs.HttpPost.Labels }}
           {{ $key }}: "{{ $value }}"
        {{- end }}
      {{- end }}
    relabel_configs:
      - source_labels: ["__address__"]
        target_label: "__param_target"
      - source_labels: ["__address__"]
        regex: "(https?)://(.+?)/(.*)"
        replacement: "$1"
        target_label: "http_protocol"
      - source_labels: ["__address__"]
        regex: "(https?)://(.+?)/(.*)"
        replacement: "$2"
        target_label: "target"
      - source_labels: ["__address__"]
        regex: "(https?)://(.+?)(/.*)"
        replacement: "$3"
        target_label: "uri"
      - source_labels: ["node_ip"]
        target_label: "instance"
      - target_label: "__address__"
        replacement: "127.0.0.1:9115"
{{- end }}

{{- if .DefaultScrapeConfigs.Tcp }}
  - job_name: blackbox_tcp_default
    scrape_interval: 30s
    metrics_path: /probe
    params:
      module: [ "tcp_default" ]
    static_configs:
      - targets:
        {{- range .DefaultScrapeConfigs.Tcp.Targets }}
        - "{{ . }}"
        {{- end }}
        labels:
        {{- range $key, $value := .DefaultScrapeConfigs.Tcp.Labels }}
           {{ $key }}: "{{ $value }}"
        {{- end }}
    relabel_configs:
      - source_labels: ["__address__"]
        target_label: "__param_target"
      - source_labels: ["__address__"]
        regex: "(.*)"
        replacement: "$1"
        target_label: "target"
      - source_labels: ["__address__"]
        regex: "(.+?):(.*)"
        replacement: "$2"
        target_label: "tcp_port"
      - source_labels: ["node_ip"]
        target_label: "instance"
      - target_label: "__address__"
        replacement: "127.0.0.1:9115"
{{- end }}

{{- if .DefaultScrapeConfigs.Icmp }}
  - job_name: blackbox_icmp_default
    scrape_interval: 30s
    metrics_path: /probe
    params:
      module: [ "icmp_default" ]
    static_configs:
       - targets:
         {{- range .DefaultScrapeConfigs.Icmp.Targets }}
         - "{{ . }}"
         {{- end }}
         labels:
         {{- range $key, $value := .DefaultScrapeConfigs.Icmp.Labels }}
            {{ $key }}: "{{ $value }}"
         {{- end }}
    relabel_configs:
      - source_labels: ["__address__"]
        target_label: "__param_target"
      - source_labels: ["__address__"]
        regex: "(.*)"
        replacement: "$1"
        target_label: "target"
      - source_labels: ["node_ip"]
        target_label: "instance"
      - target_label: "__address__"
        replacement: "127.0.0.1:9115"
{{- end }}

{{- range .ScrapeConfigs }}
  - job_name: {{ quote .JobName }}
    {{- if .ScrapeInterval }}
    scrape_interval: {{ .ScrapeInterval }}
    {{- end }}
    {{- if .MetricsPath }}
    metrics_path: {{ .MetricsPath }}
    {{- end }}
    {{- if .Params }}
    params:
      {{- range $key, $values := .Params }}
      {{ $key }}:
        {{- range $values }}
        - {{ quote . }}
        {{- end }}
      {{- end }}
    {{- end }}
    {{- if .StaticConfigs }}
    static_configs:
      {{- range .StaticConfigs }}
      - targets:
          {{- range .Targets }}
          - {{ quote . }}
          {{- end }}
        {{- if .Labels }}
        labels:
          {{- range $key, $value := .Labels }}
          {{ $key }}: {{ quote $value }}
          {{- end }}
        {{- end }}
      {{- end }}
    {{- end }}
    {{- if .RelabelConfigs }}
    relabel_configs:
      {{- range .RelabelConfigs }}
      - {{- if .SourceLabels }}
        source_labels: [ {{- range $i, $label := .SourceLabels }}{{if $i}}, {{end}}"{{ $label }}"{{- end }} ]
        {{- end }}
        {{- if .TargetLabel }}
        target_label: "{{ .TargetLabel }}"
        {{- end }}
        {{- if .Regex }}
        regex: "{{ .Regex }}"
        {{- end }}
        {{- if .Replacement }}
        replacement: "{{ .Replacement }}"
        {{- end }}
        {{- if .Action }}
        action: "{{ .Action }}"
        {{- end }}
      {{- end }}
    {{- end }}
{{- end }}

{{- if .RemoteWrite }}
remote_write:
  {{- range .RemoteWrite }}
  - url: {{ quote .URL }}
    {{- if .Name }}
    name: {{ quote .Name }}
    {{- end }}
  {{- end }}
{{- end }}