package model

import (
	"boce-service/utils/log"
	"encoding/json"
	"fmt"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func (t Item) TableName() string {
	return "item"
}

const (
	ITEM_ON_STATUS  = 1
	ITEM_OFF_STATUS = 0
)

const (
	ITEM_DEFAULT_CONFIG  = 0 // 使用默认配置
	ITEM_ADVANCED_CONFIG = 1 // 使用高级配置
)
const (
	ITEM_HTTP_GET_Method  = "HTTP_GET"
	ITEM_HTTP_POST_Method = "HTTP_POST"
	ITEM_TCP_Method       = "TCP"
	ITEM_UDP_Method       = "UDP" // 暂未支持
	ITEM_ICMP_Method      = "ICMP"
	ITEM_DNS_Method       = "DNS"
)

type Item struct {
	gorm.Model
	Id         int    `gorm:"primaryKey" json:"id"`
	Name       string `gorm:"column:name" json:"name"`
	Target     string `gorm:"column:target" json:"target"`
	Method     string `gorm:"column:method" json:"method"`
	Status     int    `gorm:"column:status" json:"status"`
	Owner      string `gorm:"column:owner" json:"owner"`
	Interval   int    `gorm:"column:interval" json:"interval"`
	ItemData   string `gorm:"column:item_data" json:"item_data"`
	IsAdvanced int    `gorm:"column:is_advanced;default:0" json:"is_advanced"` // 0=使用默认配置，1=使用高级配置
}

// HTTPGetData HTTP方法的数据结构
type HTTPGetData struct {
	HTTPProtocol  string            `json:"http_protocol"`
	SSL           bool              `json:"ssl"`
	URI           string            `json:"uri"`
	RequestMethod string            `json:"requestMethod"`
	Headers       map[string]string `json:"headers,omitempty"`
}

// HTTPPostData HTTP方法的数据结构
type HTTPPostData struct {
	HTTPProtocol  string            `json:"http_protocol"`
	URI           string            `json:"uri"`
	RequestMethod string            `json:"requestMethod"`
	Headers       map[string]string `json:"headers,omitempty"`
	Body          string            `json:"body,omitempty"`
}

// TCPData TCP方法的数据结构
type TCPData struct {
	Host string `json:"host"`
	Port int    `json:"port"`
}

// ICMPData ICMP方法的数据结构
type ICMPData struct {
	Host string `json:"host"`
}

// DNSData DNS方法的数据结构
type DNSData struct {
	QueryName string `json:"query_name"`
	QueryType string `json:"query_type"`
}

// GetDataStruct 根据Method返回对应的数据结构
func (item *Item) GetDataStruct() (interface{}, interface{}, error) {
	if item.ItemData == "" {
		return nil, nil, fmt.Errorf("data字段为空")
	}

	switch item.Method {
	case ITEM_HTTP_GET_Method:
		var probeData HTTPProbe
		var itemData HTTPGetData
		if err := json.Unmarshal([]byte(item.ItemData), &probeData); err != nil {
			return nil, nil, fmt.Errorf("解析HTTP ProbeData数据失败: %v", err)
		}
		if err := json.Unmarshal([]byte(item.ItemData), &itemData); err != nil {
			return nil, nil, fmt.Errorf("解析HTTP ItemData数据失败: %v", err)
		}
		return probeData, itemData, nil
	case ITEM_HTTP_POST_Method:
		var probeData HTTPProbe
		var itemData HTTPPostData
		if err := json.Unmarshal([]byte(item.ItemData), &probeData); err != nil {
			return nil, nil, fmt.Errorf("解析HTTP ProbeData数据失败: %v", err)
		}
		if err := json.Unmarshal([]byte(item.ItemData), &itemData); err != nil {
			return nil, nil, fmt.Errorf("解析HTTP ItemData数据失败: %v", err)
		}
		return probeData, itemData, nil
	case ITEM_TCP_Method:
		var probeData TCPData
		var itemData TCPProbe
		if err := json.Unmarshal([]byte(item.ItemData), &probeData); err != nil {
			return nil, nil, fmt.Errorf("解析TCP ProbeData数据失败: %v", err)
		}
		if err := json.Unmarshal([]byte(item.ItemData), &itemData); err != nil {
			return nil, nil, fmt.Errorf("解析TCP ItemData数据失败: %v", err)
		}
		return probeData, itemData, nil
	case ITEM_ICMP_Method:
		var probeData ICMPProbe
		var itemData ICMPData
		if err := json.Unmarshal([]byte(item.ItemData), &probeData); err != nil {
			return nil, nil, fmt.Errorf("解析ICMP ProbeData数据失败: %v", err)
		}
		if err := json.Unmarshal([]byte(item.ItemData), &itemData); err != nil {
			return nil, nil, fmt.Errorf("解析ICMP ItemData数据失败: %v", err)
		}
		return probeData, itemData, nil
	case ITEM_DNS_Method:
		var probeData DNSProbe
		var itemData DNSData
		if err := json.Unmarshal([]byte(item.ItemData), &probeData); err != nil {
			return nil, nil, fmt.Errorf("解析TCP ProbeData数据失败: %v", err)
		}
		if err := json.Unmarshal([]byte(item.ItemData), &itemData); err != nil {
			return nil, nil, fmt.Errorf("解析TCP ItemData数据失败: %v", err)
		}
		return probeData, itemData, nil
	default:
		return nil, nil, fmt.Errorf("未知的方法类型: %d", item.Method)
	}
}

// SetDataStruct 设置数据结构到Data字段
func (item *Item) SetDataStruct(data interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("序列化数据失败: %v", err)
	}
	item.ItemData = string(jsonData)
	return nil
}

// BatchCreateOrUpdateItems 批量插入或更新Item记录
func BatchCreateOrUpdateItems(records []Item) error {
	if len(records) == 0 {
		return nil
	}

	// 检查数据库连接是否已初始化
	if DB == nil {
		return fmt.Errorf("数据库连接未初始化")
	}

	log.Infof("开始批量处理 %d 条Item记录", len(records))

	// 使用事务处理
	return DB.Transaction(func(tx *gorm.DB) error {
		// 分批处理，每批100条记录
		batchSize := 100
		for i := 0; i < len(records); i += batchSize {
			end := i + batchSize
			if end > len(records) {
				end = len(records)
			}

			batch := records[i:end]
			log.Infof("处理Item批次 %d-%d", i, end-1)

			// 使用 Upsert 操作，基于name字段判断是否存在
			if err := tx.Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: "name"}},
				DoUpdates: clause.AssignmentColumns([]string{
					"method", "status", "owner", "interval", "data", "updated_at",
				}),
			}).Create(&batch).Error; err != nil {
				log.Errorf("批量插入Item失败: %v", err)
				return err
			}
		}

		log.Infof("成功批量处理 %d 条Item记录", len(records))
		return nil
	})
}

// BatchCreateOrUpdateItemsByID 基于ID进行批量插入或更新
func BatchCreateOrUpdateItemsByID(records []Item) error {
	if len(records) == 0 {
		return nil
	}

	// 检查数据库连接是否已初始化
	if DB == nil {
		return fmt.Errorf("数据库连接未初始化")
	}

	log.Infof("开始批量处理 %d 条Item记录(基于ID)", len(records))

	// 使用事务处理
	return DB.Transaction(func(tx *gorm.DB) error {
		// 分批处理，每批100条记录
		batchSize := 100
		for i := 0; i < len(records); i += batchSize {
			end := i + batchSize
			if end > len(records) {
				end = len(records)
			}

			batch := records[i:end]
			log.Infof("处理Item批次 %d-%d", i, end-1)

			// 使用 Upsert 操作，基于id字段判断是否存在
			if err := tx.Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: "id"}},
				DoUpdates: clause.AssignmentColumns([]string{
					"name", "method", "status", "owner", "interval", "data", "updated_at",
				}),
			}).Create(&batch).Error; err != nil {
				log.Errorf("批量插入Item失败: %v", err)
				return err
			}
		}

		log.Infof("成功批量处理 %d 条Item记录", len(records))
		return nil
	})
}

// BatchCreateOrUpdateItemsManual 手动处理批量插入或更新（逐条检查）
func BatchCreateOrUpdateItemsManual(records []Item) error {
	if len(records) == 0 {
		return nil
	}

	// 检查数据库连接是否已初始化
	if DB == nil {
		return fmt.Errorf("数据库连接未初始化")
	}

	log.Infof("开始手动批量处理 %d 条Item记录", len(records))

	// 使用事务处理
	return DB.Transaction(func(tx *gorm.DB) error {
		// 分批处理，每批50条记录
		batchSize := 50
		for i := 0; i < len(records); i += batchSize {
			end := i + batchSize
			if end > len(records) {
				end = len(records)
			}

			batch := records[i:end]
			log.Infof("手动处理Item批次 %d-%d", i, end-1)

			for _, record := range batch {
				var existingRecord Item
				err := tx.Where("name = ?", record.Name).First(&existingRecord).Error

				if err == gorm.ErrRecordNotFound {
					// 记录不存在，创建新记录
					if err := tx.Create(&record).Error; err != nil {
						log.Errorf("创建Item记录失败: %v", err)
						return err
					}
					log.Infof("成功插入新的Item记录，Name: %s", record.Name)
				} else if err != nil {
					// 查询出错
					log.Errorf("查询Item记录失败: %v", err)
					return err
				} else {
					// 记录存在，更新记录
					if err := tx.Model(&existingRecord).Updates(record).Error; err != nil {
						log.Errorf("更新Item记录失败: %v", err)
						return err
					}
					log.Infof("成功更新Item记录，Name: %s", record.Name)
				}
			}
		}

		log.Infof("成功手动处理 %d 条Item记录", len(records))
		return nil
	})
}

// CreateOrUpdateItem 插入单条Item记录，如果Name已存在则更新
func CreateOrUpdateItem(record Item) error {
	if record.Name == "" {
		return fmt.Errorf("Item名称不能为空")
	}

	// 检查数据库连接是否已初始化
	if DB == nil {
		return fmt.Errorf("数据库连接未初始化")
	}

	var existingRecord Item
	err := DB.Where("name = ?", record.Name).First(&existingRecord).Error

	if err == gorm.ErrRecordNotFound {
		// 记录不存在，创建新记录
		if err := DB.Create(&record).Error; err != nil {
			return fmt.Errorf("创建Item记录失败: %v", err)
		}
		log.Infof("成功插入新的Item记录，Name: %s", record.Name)
	} else if err != nil {
		// 查询出错
		return fmt.Errorf("查询Item记录失败: %v", err)
	} else {
		// 记录存在，更新记录
		if err := DB.Model(&existingRecord).Updates(record).Error; err != nil {
			return fmt.Errorf("更新Item记录失败: %v", err)
		}
		log.Infof("成功更新Item记录，Name: %s", record.Name)
	}

	return nil
}

// GetItemsByFilter 根据过滤条件查询Item记录
func GetItemsByFilter(filter string) []Item {
	var items []Item
	db := GetDB()
	db.Where(filter).Find(&items)
	return items
}

// GetItemsByStatus 根据状态查询Item记录
func GetItemsByStatus(status int) []Item {
	var items []Item
	db := GetDB()
	db.Where("status = ?", status).Find(&items)
	return items
}

// GetItemsByOwner 根据所有者查询Item记录
func GetItemsByOwner(owner string) []Item {
	var items []Item
	db := GetDB()
	db.Where("owner = ?", owner).Find(&items)
	return items
}

type ItemData struct {
	Id         int    `json:"id"`
	Name       string `json:"name"`
	Target     string `json:"target"`
	Method     string `json:"method"`
	Owner      string `json:"owner"`
	ItemData   string `json:"item_data"`
	Interval   int    `json:"interval"`
	Status     int    `json:"status"`
	IsAdvanced int    `json:"is_advanced"`
}

// GetItemsData 获取Items数据（业务逻辑）
func GetItemsData() ([]ItemData, []Item, error) {
	items := GetItemsByFilter("status=1")
	if len(items) == 0 {
		return nil, nil, fmt.Errorf("未找到Item记录")
	}

	var data []ItemData
	for _, item := range items {
		line := ItemData{
			Id:         item.Id,
			Name:       item.Name,
			Target:     item.Target,
			Method:     item.Method,
			Owner:      item.Owner,
			ItemData:   item.ItemData,
			Interval:   item.Interval,
			Status:     item.Status,
			IsAdvanced: item.IsAdvanced,
		}
		data = append(data, line)
	}

	return data, items, nil
}

// UpdateItemById 按ID更新Item记录
func UpdateItemById(record Item) error {
	if record.Id <= 0 {
		return fmt.Errorf("Item ID不能为空或小于等于0")
	}

	// 检查数据库连接是否已初始化
	if DB == nil {
		return fmt.Errorf("数据库连接未初始化")
	}

	var existingRecord Item
	err := DB.Where("id = ?", record.Id).First(&existingRecord).Error

	if err == gorm.ErrRecordNotFound {
		return fmt.Errorf("ID为 %d 的Item记录不存在", record.Id)
	} else if err != nil {
		return fmt.Errorf("查询Item记录失败: %v", err)
	}

	// 如果没有提供Name，则根据Target和Method生成
	if record.Name == "" {
		record.Name = record.Target + "_" + record.Method
	}

	// 强制DNS类型使用高级配置
	if record.Method == ITEM_DNS_Method {
		record.IsAdvanced = ITEM_ADVANCED_CONFIG
		log.Infof("DNS类型Item强制设置为高级配置: %s", record.Name)
	}

	// 更新记录
	if err := DB.Model(&existingRecord).Updates(record).Error; err != nil {
		return fmt.Errorf("更新Item记录失败: %v", err)
	}

	log.Infof("成功更新Item记录，ID: %d, Name: %s", record.Id, record.Name)
	return nil
}
