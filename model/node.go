package model

import (
	"boce-service/utils/log"
	"fmt"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

func (t Node) TableName() string {
	return "node"
}

const (
	NODE_ON_STATUS  = 1
	NODE_OFF_STATUS = 0
)

type Node struct {
	gorm.Model
	Id         uuid.UUID `gorm:"type:char(36);primaryKey"`
	Accuracy   string    `gorm:"column:accuracy" json:"accuracy"`
	IpAddr     string    `gorm:"column:ip_addr" json:"ip_addr"`
	Continent  string    `gorm:"column:continent" json:"continent"`
	Country    string    `gorm:"column:country" json:"country"`
	Province   string    `gorm:"column:province" json:"province"`
	City       string    `gorm:"column:city" json:"city"`
	District   string    `gorm:"column:district" json:"district"`
	AreaCode   string    `gorm:"column:area_code" json:"area_code"`
	AdCode     string    `gorm:"column:ad_code" json:"ad_code"`
	Isp        string    `gorm:"column:isp" json:"isp"`
	IspCode    string    `gorm:"column:isp_code" json:"isp_code"`
	ZipCode    string    `gorm:"column:zip_code" json:"zip_code"`
	AsNumber   string    `gorm:"column:as_number" json:"as_number"`
	Lngwgs     string    `gorm:"column:lngwgs" json:"lngwgs"`
	Latwgs     string    `gorm:"column:latwgs" json:"latwgs"`
	Status     int       `gorm:"column:status" json:"status"`
	Owner      string    `gorm:"column:owner" json:"owner"`
	DnsId      int       `gorm:"column:dns_id" json:"dns_id"`
	IpProtocol string    `gorm:"column:ip_protocol" json:"ip_protocol"`
}

// InsertNode 插入单条Node记录，如果IP已存在则更新
func CreateOrUpdateNode(record Node) error {
	if record.IpAddr == "" {
		return fmt.Errorf("IP地址不能为空")
	}

	// 检查数据库连接是否已初始化
	if DB == nil {
		return fmt.Errorf("数据库连接未初始化")
	}

	var existingRecord Node
	err := DB.Where("ip_addr = ?", record.IpAddr).First(&existingRecord).Error

	if err == gorm.ErrRecordNotFound {
		// 记录不存在，创建新记录
		if err := DB.Create(&record).Error; err != nil {
			return fmt.Errorf("创建记录失败: %v", err)
		}
		log.Infof("成功插入新的Node记录，IP: %s", record.IpAddr)
	} else if err != nil {
		// 查询出错
		return fmt.Errorf("查询记录失败: %v", err)
	} else {
		// 记录存在，更新记录
		if err := DB.Model(&existingRecord).Updates(record).Error; err != nil {
			return fmt.Errorf("更新记录失败: %v", err)
		}
		log.Infof("成功更新Node记录，IP: %s", record.IpAddr)
	}

	return nil
}

// CreateNode 仅创建新记录，如果IP已存在则返回错误
func CreateNode(record Node) error {
	if record.IpAddr == "" {
		return fmt.Errorf("IP地址不能为空")
	}

	// 检查数据库连接是否已初始化
	if DB == nil {
		return fmt.Errorf("数据库连接未初始化")
	}

	// 检查是否已存在
	var count int64
	if err := DB.Model(&Node{}).Where("ip_addr = ?", record.IpAddr).Count(&count).Error; err != nil {
		return fmt.Errorf("检查记录是否存在失败: %v", err)
	}

	if count > 0 {
		return fmt.Errorf("IP地址 %s 已存在", record.IpAddr)
	}

	// 创建新记录
	if err := DB.Create(&record).Error; err != nil {
		return fmt.Errorf("创建记录失败: %v", err)
	}

	log.Infof("成功创建新的Node记录，IP: %s", record.IpAddr)
	return nil
}

// UpdateNode 仅更新已存在的记录，如果记录不存在则返回错误
func UpdateNode(record Node) error {
	if record.IpAddr == "" {
		return fmt.Errorf("IP地址不能为空")
	}

	// 检查数据库连接是否已初始化
	if DB == nil {
		return fmt.Errorf("数据库连接未初始化")
	}

	var existingRecord Node
	err := DB.Where("ip_addr = ?", record.IpAddr).First(&existingRecord).Error

	if err == gorm.ErrRecordNotFound {
		return fmt.Errorf("IP地址 %s 不存在", record.IpAddr)
	} else if err != nil {
		return fmt.Errorf("查询记录失败: %v", err)
	}

	// 更新记录
	if err := DB.Model(&existingRecord).Updates(record).Error; err != nil {
		return fmt.Errorf("更新记录失败: %v", err)
	}

	log.Infof("成功更新Node记录，IP: %s", record.IpAddr)
	return nil
}

func GetNodeByIp(ip string) (Node, error) {
	var node Node
	err := DB.Where("ip_addr = ? and status = ?", ip, NODE_ON_STATUS).First(&node).Error
	if err != nil {
		return node, err
	}
	return node, nil
}

// GetAllNodes 获取所有节点信息
func GetAllNodes() []Node {
	var nodes []Node
	db := GetDB()
	db.Where("status = ?", NODE_ON_STATUS).Find(&nodes)
	return nodes
}

// GetNodesByFilter 根据过滤条件获取节点信息
func GetNodesByFilter(filter string) []Node {
	var nodes []Node
	db := GetDB()
	if filter == "" {
		db.Find(&nodes)
	} else {
		db.Where(filter).Find(&nodes)
	}
	return nodes
}

type NodeData struct {
	Id         uuid.UUID `json:"id"`
	IpAddr     string    `json:"ip_addr"`
	Continent  string    `json:"continent"`
	City       string    `json:"city"`
	Province   string    `json:"province"`
	Isp        string    `json:"isp"`
	IspCode    string    `json:"isp_code"`
	DnsAddr    string    `json:"dns_addr"`
	ZipCode    string    `json:"zip_code"`
	Latwgs     string    `json:"latwgs"`
	Lngwgs     string    `json:"lngwgs"`
	IpProtocol string    `json:"ip_protocol"`
	Status     int       `json:"status"`
}

// GetNodeData 获取Node数据（业务逻辑）
func GetNodeData(ipAddr string) (NodeData, error) {
	if ipAddr == "" {
		return NodeData{}, fmt.Errorf("IP地址不能为空")
	}

	node, err := GetNodeByIp(ipAddr)
	if err != nil {
		return NodeData{}, fmt.Errorf("查询节点失败: %v", err)
	}

	dns := GetDNSById(node.DnsId)

	data := NodeData{
		Id:         node.Id,
		IpAddr:     node.IpAddr,
		Continent:  node.Continent,
		City:       node.City,
		Province:   node.Province,
		Isp:        node.Isp,
		IspCode:    node.IspCode,
		DnsAddr:    dns.IpAddr,
		ZipCode:    node.ZipCode,
		Latwgs:     node.Latwgs,
		Lngwgs:     node.Lngwgs,
		IpProtocol: node.IpProtocol,
		Status:     node.Status,
	}

	return data, nil
}

// UpdateNodeStatus 更新节点状态
func UpdateNodeStatus(ipAddr string, status int) error {
	if ipAddr == "" {
		return fmt.Errorf("IP地址不能为空")
	}

	// 检查数据库连接是否已初始化
	if DB == nil {
		return fmt.Errorf("数据库连接未初始化")
	}

	// 更新节点状态
	result := DB.Model(&Node{}).Where("ip_addr = ?", ipAddr).Update("status", status)
	if result.Error != nil {
		return fmt.Errorf("更新节点状态失败: %v", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("节点不存在或状态未改变: %s", ipAddr)
	}

	log.Infof("成功更新节点 %s 状态为 %d", ipAddr, status)
	return nil
}
